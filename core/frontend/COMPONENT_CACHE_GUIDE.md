# 组件缓存使用指南

## ⚠️ 重要提醒：数据更新及时性

`enableComponentCache: true` 确实可能导致数据更新不及时的问题。我们已经对缓存系统进行了重大改进，以解决这个问题。

## 🔧 改进后的缓存特性

### 1. 智能TTL（生存时间）
- **默认TTL**: 5分钟（300秒）
- **可配置**: 根据数据类型设置不同的TTL
- **自动过期**: 超时后自动失效

### 2. 选择性缓存
- **包含模式**: 只缓存指定的数据字段
- **排除模式**: 排除敏感或实时数据
- **智能识别**: 自动跳过函数和复杂对象

### 3. 版本控制
- **数据版本**: 通过版本号检测数据变化
- **自动失效**: 版本不匹配时自动清除缓存
- **级联更新**: 相关数据同步更新

## 📝 使用方法

### 基础用法

```javascript
export default {
  name: 'MyComponent',
  
  // 简单启用缓存（5分钟TTL）
  cache: 'my-component-cache',
  
  // 或者使用对象配置
  cache: {
    key: 'my-component-cache',
    ttl: 300000 // 5分钟
  }
}
```

### 选择性缓存

```javascript
export default {
  name: 'UserProfile',
  cache: {
    key: 'user-profile',
    ttl: 600000, // 10分钟
    include: ['userInfo', 'preferences'], // 只缓存这些字段
    exclude: ['sensitiveData', 'realtimeStatus'] // 排除这些字段
  },
  
  data() {
    return {
      userInfo: { name: '张三', email: '<EMAIL>' }, // ✅ 会被缓存
      preferences: { theme: 'dark', lang: 'zh' }, // ✅ 会被缓存
      sensitiveData: { token: 'secret' }, // ❌ 不会被缓存
      realtimeStatus: { online: true, lastSeen: Date.now() } // ❌ 不会被缓存
    }
  }
}
```

### 动态缓存键

```javascript
export default {
  name: 'UserDashboard',
  cache: {
    key: function() {
      // 根据用户ID生成不同的缓存键
      return `user-dashboard-${this.$store.state.user.id}`
    },
    ttl: 300000
  }
}
```

### 自定义验证

```javascript
export default {
  name: 'ProductList',
  cache: {
    key: 'product-list',
    ttl: 600000,
    validate: function(cached) {
      // 检查产品类别是否变化
      const currentCategory = this.$route.params.category
      return cached.category === currentCategory
    }
  },
  
  data() {
    return {
      category: this.$route.params.category,
      products: []
    }
  }
}
```

### 版本控制

```javascript
export default {
  name: 'DataTable',
  cache: {
    key: 'data-table',
    ttl: 300000
  },
  
  data() {
    return {
      tableData: [],
      dataVersion: 1
    }
  },
  
  methods: {
    // 实现版本获取方法
    getDataVersion() {
      return this.dataVersion
    },
    
    updateData() {
      // 更新数据时增加版本号
      this.tableData = newData
      this.dataVersion++
    }
  }
}
```

## 🎯 最佳实践

### 1. 什么时候使用缓存

**✅ 适合缓存的场景：**
- 用户配置信息
- 静态配置数据
- 计算密集的结果
- 不经常变化的列表数据
- 用户偏好设置

**❌ 不适合缓存的场景：**
- 实时数据（股价、聊天消息）
- 敏感信息（密码、令牌）
- 频繁变化的状态
- 用户输入数据
- 表单数据

### 2. TTL设置建议

```javascript
// 根据数据特性设置不同的TTL
const cacheConfig = {
  // 用户信息 - 中等更新频率
  userInfo: { ttl: 300000 }, // 5分钟
  
  // 系统配置 - 很少变化
  systemConfig: { ttl: 1800000 }, // 30分钟
  
  // 产品列表 - 定期更新
  productList: { ttl: 600000 }, // 10分钟
  
  // 实时数据 - 不缓存
  realtimeData: { cache: false }
}
```

### 3. 内存管理

```javascript
// 监控内存使用
console.log('缓存统计:', this.$componentCache.getStats())

// 手动清理
this.$componentCache.cleanupExpired()

// 清除特定缓存
this.clearCache()

// 刷新缓存
this.refreshCache()
```

## 🔍 调试和监控

### 开发模式调试

```javascript
// 在组件中查看缓存状态
mounted() {
  if (process.env.NODE_ENV === 'development') {
    console.log('从缓存恢复:', this._fromCache)
    console.log('缓存时间戳:', this._cacheTimestamp)
    console.log('缓存统计:', this.$componentCache.getStats())
  }
}
```

### 缓存统计

```javascript
// 获取全局缓存统计
const stats = this.$componentCache.getStats()
console.log('缓存命中率:', stats.hitRate)
console.log('缓存大小:', stats.size)
console.log('缓存键列表:', stats.keys)
```

### 内存监控

```javascript
// 检查内存使用
import { memoryMonitor } from '@/utils/componentCache'

const memoryUsage = memoryMonitor.getMemoryUsage()
console.log('内存使用:', memoryUsage)

const hasMemoryPressure = memoryMonitor.checkMemoryPressure()
if (hasMemoryPressure) {
  console.warn('内存压力过大，建议清理缓存')
}
```

## ⚙️ 全局配置

### 禁用组件缓存

如果您担心数据更新及时性问题，可以完全禁用组件缓存：

```javascript
// 在 main.js 中
Vue.use(PerformancePlugin, {
  enableComponentCache: false, // 禁用组件缓存
  // 其他配置...
})
```

### 调整缓存大小

```javascript
// 在性能插件配置中
Vue.use(PerformancePlugin, {
  enableComponentCache: true,
  componentCacheOptions: {
    maxSize: 30, // 最大缓存30个组件（默认50）
    defaultTTL: 180000 // 默认TTL 3分钟（默认5分钟）
  }
})
```

## 🚨 注意事项

1. **数据一致性**: 缓存可能导致数据不一致，特别是在多用户环境中
2. **内存占用**: 缓存会占用额外内存，需要合理控制缓存大小
3. **调试困难**: 缓存可能使调试变得复杂，建议在开发模式下禁用
4. **安全风险**: 不要缓存敏感数据，如密码、令牌等

## 🔄 迁移指南

如果您决定禁用组件缓存，可以：

1. **全局禁用**：设置 `enableComponentCache: false`
2. **选择性禁用**：在特定组件中设置 `cache: false`
3. **渐进式迁移**：先在关键组件中禁用，然后逐步扩展

## 📞 获取帮助

如果遇到缓存相关问题：

1. 检查控制台是否有缓存相关的警告或错误
2. 使用 `this.$componentCache.getStats()` 查看缓存统计
3. 在开发模式下使用缓存调试面板（Ctrl+Shift+C）
4. 查看内存使用情况，确保没有内存泄漏

---

*建议：在生产环境中谨慎使用组件缓存，特别是对于数据实时性要求高的应用。*
