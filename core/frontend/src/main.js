import Vue from 'vue'
import App from './App.vue'
import router from './router';
import store from './store';
import './styles/global.less';
import './directives/permission'
import './directives/role'
// import './registerServiceWorker'
import './plugins'
import PerformancePlugin from './plugins/performance';
import {commonMixin} from './mixin/commonMixin'

Vue.mixin(commonMixin);

// 安装性能优化插件
Vue.use(PerformancePlugin, {
  enablePerformanceMonitoring: true,
  enableComponentCache: true,
  enableDOMOptimization: true,
  enableOfflineSupport: true,
  enableMemoryMonitoring: true,
  router
})

// 在开发模式下加载缓存修复工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/cacheFixScript.js')
}

Vue.config.productionTip = false

window.addEventListener("message", (e) => {
  const method = e.data && e.data.method

  if (method === 'getUserInfo') {
    const userInfo = store.getters['user/userInfo'];
    window.parent && window.parent.postMessage(userInfo, "*");
  }
})

// Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive.
// document.addEventListener('touchmove', function (event) {
//   event.preventDefault();
// }, {
//   passive: false
// });

new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    
  },
}).$mount('#app')
