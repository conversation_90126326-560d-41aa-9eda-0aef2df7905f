// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  app: {
      pageTransition: { name: 'message', mode: 'out-in' },
      head: {
          htmlAttrs: {
            lang: 'zh-CN'
          },
          templateParams: {
              separator: '-'
          },
          titleTemplate: '%s %separator %siteName',
          meta: [
              { charset: 'utf-8' },
              { name: 'viewport', content: 'width=device-width, initial-scale=1, user-scalable=no' },
              { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' },
              { name: 'renderer', content: 'webkit'},
              { name: 'format-detection', content: 'telephone=no'},
              { name: 'theme-color', content: '#ffffff' },
              { name: 'apple-mobile-web-app-capable', content: 'yes' },
              { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
              { name: 'apple-mobile-web-app-title', content: 'FET' },
              { name: 'application-name', content: 'FET' },
              { name: 'msapplication-TileColor', content: '#ffffff' },
              { name: 'msapplication-config', content: '/browserconfig.xml' }
          ],
          link: [
              { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
              { rel: 'manifest', href: '/manifest.json' },
              { rel: 'apple-touch-icon', sizes: '180x180', href: '/icons/apple-touch-icon.png' },
              { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/icons/favicon-32x32.png' },
              { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/icons/favicon-16x16.png' },
              { rel: 'mask-icon', href: '/icons/safari-pinned-tab.svg', color: '#5bbad5' }
          ],
          script: [
            { src: "https://hm.baidu.com/hm.js?41f9205fdbdd17b27ff84d45dbec8337", type: "text/javascript", async: true }
          ]
      }
  },

  site: {
      url: process.env.NUXT_PUBLIC_SITE_URL || 'https://finevent.top',
      name: 'FET',
      description: '第二手信息聚合平台，提供投资资讯、市场分析、公司调研等优质内容',
      defaultLocale: 'zh-CN',
      trailingSlash: false,
      cacheMaxAgeSeconds: 24 * 3600,
      autoLastmod: true,
      identity: {
          type: 'Organization',
          name: 'FET',
          url: process.env.NUXT_PUBLIC_SITE_URL || 'https://finevent.top',
          logo: `${process.env.NUXT_PUBLIC_SITE_URL || 'https://finevent.top'}/logo.png`
      }
  },

  ogImage: {
      enabled: true,
      defaults: {
          component: 'OgImageBasic',
          width: 1200,
          height: 630,
          cacheMaxAgeSeconds: 60 * 60 * 24 * 7 // 7天缓存
      }
  },

  robots: {
      enabled: true,
      blockNonSeoBots: true,
      disallow: ['/api/', '/admin/', '/_nuxt/']
  },



  schemaOrg: {
      enabled: true,
      identity: {
          type: 'Organization',
          name: 'FET',
          description: '第二手信息聚合平台',
          url: process.env.NUXT_PUBLIC_SITE_URL || 'https://finevent.top',
          logo: `${process.env.NUXT_PUBLIC_SITE_URL || 'https://finevent.top'}/logo.png`,
          sameAs: []
      }
  },

  linkChecker: {
      enabled: true,
      excludeLinks: ['mailto:', 'tel:', 'sms:']
  },

  seo: {
      fallbackTitle: false,
      automaticDefaults: true
  },

  modules: [
    '@nuxtjs/device',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    'dayjs-nuxt',
    '@vite-pwa/nuxt',
    '@nuxtjs/seo',
    '@nuxt/ui',
    'nuxt-og-image',
    '@nuxtjs/sitemap'
  ],

  sitemap: {
      enabled: true,
      cacheMaxAgeSeconds: 6 * 60 * 60,
      autoLastmod: true,
      defaults: {
          changefreq: 'daily',
          lastmod: new Date(),
          priority: 0.8,
      },
      urls: [
          {
              loc: '/',
              changefreq: 'hourly',
              priority: 1.0
          },
          {
              loc: '/about',
              changefreq: 'monthly',
              priority: 0.7
          },
          {
              loc: '/post',
              changefreq: 'daily',
              priority: 0.8
          },
          {
              loc: '/tag',
              changefreq: 'daily',
              priority: 0.8
          },
          {
              loc: '/media',
              changefreq: 'daily',
              priority: 0.7
          },
          {
              loc: '/summary',
              changefreq: 'daily',
              priority: 0.8
          }
      ],
      sitemaps: {
        posts: {
          sources: [
              '/api/__sitemap__/posts',
          ],
          defaults: {
              changefreq: 'weekly',
              priority: 0.9
          }
        },
        tags: {
          sources: [
              '/api/__sitemap__/tags',
          ],
          defaults: {
              changefreq: 'weekly',
              priority: 0.7
          }
        },
        medias: {
          sources: [
              '/api/__sitemap__/medias',
          ],
          defaults: {
              changefreq: 'weekly',
              priority: 0.6
          }
        },
        summaries: {
          sources: [
              '/api/__sitemap__/summaries',
          ],
          defaults: {
              changefreq: 'weekly',
              priority: 0.8
          }
        },
      },
  },

  routeRules: {
      '/': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=300' }
      },
      '/about': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=86400' }
      },
      '/post/**': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=3600' }
      },
      '/tag/**': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=1800' }
      },
      '/category-**': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=1800' }
      },
      '/stock/**': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=1800' }
      },
      '/media/**': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=3600' }
      },
      '/summary/**': {
          ssr: true,
          headers: { 'cache-control': 's-maxage=1800' }
      },
      '/api/**': {
          cors: true,
          headers: { 'cache-control': 'no-cache, no-store, must-revalidate' } // 股票网站禁用API缓存
      }
  },

  alias: {
      "@": "./"
  },

  css: [
      'assets/styles/global.less',
      'assets/styles/typography.css',
      'assets/styles/colors.css',
      'assets/styles/animations.css',
      'assets/styles/enhancements.css'
  ],

  typescript: {
      shim: false
  },

  dayjs: {
      locales: ['en', 'zh-cn'],
      plugins: ['relativeTime', 'utc', 'timezone'],
      defaultLocale: 'zh-cn'
  },

  pwa: {
      registerType: 'autoUpdate',
      manifest: {
          name: 'FET - 第二手信息',
          short_name: 'FET',
          description: '第二手信息聚合平台，提供投资资讯、市场分析、公司调研等优质内容',
          lang: 'zh-CN',
          display: 'standalone',
          orientation: 'portrait',
          theme_color: '#ffffff',
          background_color: '#ffffff',
          start_url: '/',
          scope: '/',
          categories: ['finance', 'news', 'business'],
          icons: [
              {
                "src": "./icons/android-chrome-192x192.png",
                "sizes": "192x192",
                "type": "image/png"
              },
              {
                "src": "./icons/android-chrome-512x512.png",
                "sizes": "512x512",
                "type": "image/png"
              },
              {
                "src": "./icons/android-chrome-maskable-192x192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "maskable"
              },
              {
                "src": "./icons/android-chrome-maskable-512x512.png",
                "sizes": "512x512",
                "type": "image/png",
                "purpose": "maskable"
              },
              {
                "src": "./icons/apple-touch-icon-60x60.png",
                "sizes": "60x60",
                "type": "image/png"
              },
              {
                "src": "./icons/apple-touch-icon-76x76.png",
                "sizes": "76x76",
                "type": "image/png"
              },
              {
                "src": "./icons/apple-touch-icon-120x120.png",
                "sizes": "120x120",
                "type": "image/png"
              },
              {
                "src": "./icons/apple-touch-icon-152x152.png",
                "sizes": "152x152",
                "type": "image/png"
              },
              {
                "src": "./icons/apple-touch-icon-180x180.png",
                "sizes": "180x180",
                "type": "image/png"
              },
              {
                "src": "./icons/apple-touch-icon.png",
                "sizes": "180x180",
                "type": "image/png"
              },
              {
                "src": "./icons/favicon-16x16.png",
                "sizes": "16x16",
                "type": "image/png"
              },
              {
                "src": "./icons/favicon-32x32.png",
                "sizes": "32x32",
                "type": "image/png"
              },
              {
                "src": "./icons/msapplication-icon-144x144.png",
                "sizes": "144x144",
                "type": "image/png"
              },
              {
                "src": "./icons/mstile-150x150.png",
                "sizes": "150x150",
                "type": "image/png"
              }
          ]
      },
      workbox: {
        navigateFallback: '/',
        globPatterns: ['**/*.{js,css,html,png,svg,ico,woff2,woff}'],
        runtimeCaching: [
          // 股票网站禁用API缓存，确保数据实时性
          // {
          //   urlPattern: /^https:\/\/finevent\.top\/api\//,
          //   handler: 'StaleWhileRevalidate',
          //   options: {
          //     cacheName: 'api-cache',
          //     expiration: {
          //       maxEntries: 100,
          //       maxAgeSeconds: 60 * 60 * 24 // 24小时
          //     }
          //   }
          // },
          {
            urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'images-cache',
              expiration: {
                maxEntries: 200,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30天
              }
            }
          },
          {
            urlPattern: /\.(?:woff|woff2|ttf|eot)$/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'fonts-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1年
              }
            }
          }
        ]
      },
      client: {
        installPrompt: true,
        // you don't need to include this: only for testing purposes
        // if enabling periodic sync for update use 1 hour or so (periodicSyncForUpdates: 3600)
        periodicSyncForUpdates: 20,
      },
      devOptions: {
        enabled: true,
        suppressWarnings: true,
        navigateFallbackAllowlist: [/^\/$/],
        type: 'module',
      },
  },

  vite: {
      css: {
          preprocessorOptions: {
              less: {
                  additionalData: '@use "@/assets/styles/global.less" as *;'
              },
          },
      },
      build: {
          rollupOptions: {
              output: {
                  manualChunks: {
                      'echarts': ['echarts'],
                      'vue-echarts': ['vue-echarts'],
                      'marked': ['marked'],
                      'dayjs': ['dayjs']
                  }
              }
          }
      },
      optimizeDeps: {
          include: ['echarts', 'vue-echarts', 'marked', 'dayjs']
      }
  },

  nitro: {
      esbuild: {
        options: {
          target: 'esnext'  // 确保使用现代ES模块
        }
      },
      preset: 'node-server',
      prerender: {
        routes: ["/robots.txt", "/about"]
      },
      compressPublicAssets: true,
      minify: true,
      experimental: {
        wasm: true
      },
      devProxy: {
          "/api": {
              target: 'http://127.0.0.1:7001/',
              prependPath: true,
              changeOrigin: true,
          }
      },
      storage: {
          redis: {
              driver: 'redis',
              // Redis配置用于缓存
          }
      }
  },

  runtimeConfig: {
      // Private keys (server-side only)
      apiSecret: process.env.NUXT_API_SECRET || '123',
      // Public keys (client-side accessible)
      public: {
          apiBase: process.env.NUXT_PUBLIC_BASE_URL || 'http://127.0.0.1:7001',
          version: process.env.NUXT_PUBLIC_VERSION || 'V3.3.13',
          frontend: process.env.NUXT_PUBLIC_FRONTEND || 'http://test.finevent.top:8080',
          siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://finevent.top'
      }
  },

  build: {
    transpile: [/echarts/, 'vue-echarts', 'resize-detector'],
  },

  icon: {
    provider: 'iconify'
  },

  compatibilityDate: "2024-07-29",
})